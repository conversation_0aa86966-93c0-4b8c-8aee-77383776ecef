var g=(o,a,i)=>new Promise((p,e)=>{var m=r=>{try{n(i.next(r))}catch(l){e(l)}},u=r=>{try{n(i.throw(r))}catch(l){e(l)}},n=r=>r.done?p(r.value):Promise.resolve(r.value).then(m,u);n((i=i.apply(o,a)).next())});import{a as v,r as c,j as t}from"./react-vendor-DNThP37t.js";import{s as h}from"./utils-G5iJCnJd.js";import{P as A}from"./components-DKpPwVyy.js";import"./vendor-BttnBCBn.js";import"./supabase-vendor-DDc5weSN.js";const P=({searchQuery:o})=>{const{username:a}=v(),[i,p]=c.useState([]),[e,m]=c.useState(null),[u,n]=c.useState(!0),[r,l]=c.useState(null);c.useEffect(()=>{a&&j()},[a,o]);const j=()=>g(null,null,function*(){try{n(!0);const{data:s,error:d}=yield h.from("users").select("*").eq("user_login",a).single();if(d)throw d;m(s);let f=h.from("posts").select("*").eq("author_id",s.id).eq("status","published").order("published_at",{ascending:!1});o&&o.trim()&&(f=f.or(`title.ilike.%${o}%,content.ilike.%${o}%`));const{data:S,error:x}=yield f;if(x)throw x;p(S||[])}catch(s){l("Author not found")}finally{n(!1)}}),b=s=>s?s.split(" ").map(d=>d.charAt(0)).join("").toUpperCase().substring(0,2):"A",y=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return u?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:"Loading author..."})}):r||!e?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"error",children:r||"Author not found"})}):t.jsxs(t.Fragment,{children:[e&&t.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"40px 20px",background:"white",border:"1px solid #f0f0f0"},children:[t.jsx("div",{style:{width:"80px",height:"80px",background:"#ddd",borderRadius:"50%",margin:"0 auto 20px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"32px",fontWeight:"bold",color:"#666"},children:b(e.display_name)}),t.jsx("h1",{style:{fontSize:"32px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:e.display_name}),t.jsxs("p",{style:{color:"#666",fontSize:"16px",marginBottom:"10px"},children:["@",e.user_login]}),t.jsxs("p",{style:{color:"#999",fontSize:"14px",marginBottom:"10px"},children:["Member since ",y(e.user_registered)]}),t.jsxs("p",{style:{color:"#999",fontSize:"14px"},children:[i.length," ",i.length===1?"post":"posts"," published"]})]}),i.length===0?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:o?`No posts found by ${e==null?void 0:e.display_name} for "${o}"`:`No posts found by ${e==null?void 0:e.display_name}`})}):t.jsx("div",{className:"main-grid",children:i.map(s=>t.jsx(A,{post:s},s.id))})]})};export{P as default};
