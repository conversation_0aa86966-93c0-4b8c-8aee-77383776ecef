var P=(t,i,a)=>new Promise((r,l)=>{var m=e=>{try{d(a.next(e))}catch(n){l(n)}},p=e=>{try{d(a.throw(e))}catch(n){l(n)}},d=e=>e.done?r(e.value):Promise.resolve(e.value).then(m,p);d((a=a.apply(t,i)).next())});import{r as o,j as s}from"./react-vendor-DNThP37t.js";import{b as E}from"./utils-G5iJCnJd.js";import{S as H,P as N}from"./components-DKpPwVyy.js";import"./vendor-BttnBCBn.js";import"./supabase-vendor-DDc5weSN.js";const L=({searchQuery:t})=>{const[i,a]=o.useState([]),[r,l]=o.useState(!0),[m,p]=o.useState(null),[d,e]=o.useState(!0),[n,f]=o.useState(0),v=12;o.useEffect(()=>{g(!0)},[t]);const g=o.useCallback((c=!1)=>P(null,null,function*(){try{l(!0),p(null);const u=c?0:n,{posts:h,hasMore:b}=yield E(u,v,t);c?(a(h),f(1)):(a(x=>[...x,...h]),f(x=>x+1)),e(b)}catch(u){p("Failed to load posts")}finally{l(!1)}}),[n,t]);if(r&&i.length===0)return s.jsx("div",{className:"main-grid",children:s.jsx(H,{type:"post",count:6})});if(m)return s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"error",children:m})});if(i.length===0&&!r)return s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"loading",children:t?`No posts found for "${t}"`:"No posts available"})});const[j,...S]=i;return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"main-grid",children:[j&&!t&&s.jsx(N,{post:j,featured:!0,priority:!0}),(t?i:S).map((c,u)=>s.jsx(N,{post:c,priority:u<3},c.id))]}),d&&!t&&s.jsx("div",{className:"load-more-container",children:s.jsx("button",{onClick:()=>g(!1),disabled:r,className:"load-more-btn",children:r?s.jsxs("div",{className:"loading-inline",children:[s.jsx("div",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"8px"}}),"Loading..."]}):"Load More Posts"})})]})},F=o.memo(L);export{F as default};
