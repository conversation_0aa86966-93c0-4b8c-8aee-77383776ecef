var S=(d,r,o)=>new Promise((p,g)=>{var c=a=>{try{i(o.next(a))}catch(m){g(m)}},x=a=>{try{i(o.throw(a))}catch(m){g(m)}},i=a=>a.done?p(a.value):Promise.resolve(a.value).then(c,x);i((o=o.apply(d,r)).next())});import{a as D,r as l,j as e,L as v}from"./react-vendor-DNThP37t.js";import{s as h}from"./utils-G5iJCnJd.js";import"./components-DKpPwVyy.js";import"./vendor-BttnBCBn.js";import"./supabase-vendor-DDc5weSN.js";const C=()=>{const{slug:d}=D(),[r,o]=l.useState(null),[p,g]=l.useState([]),[c,x]=l.useState([]),[i,a]=l.useState([]),[m,y]=l.useState(!0),[N,_]=l.useState(null);l.useEffect(()=>{d&&w()},[d]);const w=()=>S(null,null,function*(){var t,s;try{y(!0);const{data:n,error:u}=yield h.from("posts").select("id, title, slug, content, author_id, published_at, status").eq("slug",d).eq("status","published").single();if(u)throw u;o(n);const[f,j,P]=yield Promise.all([h.from("posts").select("id, title, slug, published_at").eq("status","published").neq("id",n.id).order("published_at",{ascending:!1}).limit(3),h.from("post_categories").select(`
            categories (
              id,
              name,
              slug
            )
          `).eq("post_id",n.id),h.from("post_tags").select(`
            tags (
              id,
              name,
              slug
            )
          `).eq("post_id",n.id)]);g(f.data||[]),x(((t=j.data)==null?void 0:t.map(b=>b.categories))||[]),a(((s=P.data)==null?void 0:s.map(b=>b.tags))||[])}catch(n){_("Post not found")}finally{y(!1)}}),$=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),q=t=>{if(!t)return"";let s=t.replace(/<!-- wp:.*? -->/g,"");return s=s.replace(/<!-- \/wp:.*? -->/g,""),s=s.replace(/class="[^"]*wp-[^"]*"/g,""),s=s.replace(/class="[^"]*has-[^"]*"/g,""),s=s.replace(/style="[^"]*text-transform:[^"]*"/g,""),s=s.replace(/<img([^>]*)src="([^"]*)"([^>]*)>/g,(n,u,f,j)=>`<img${u}src="${f}"${j} loading="lazy" decoding="async" style="max-width: 100%; height: auto; margin: 20px 0; border-radius: 8px;" sizes="(max-width: 768px) 100vw, 800px">`),s=s.replace(/<h([1-6])([^>]*)>/g,'<h$1$2 style="margin: 30px 0 20px 0;">'),s=s.replace(/<blockquote([^>]*)>/g,'<blockquote$1 style="margin: 20px 0; padding: 15px 20px; border-left: 4px solid #ddd; background: #f9f9f9; font-style: italic;">'),s.trim()};return m?e.jsx("div",{className:"single-poem-grid",children:e.jsx("div",{className:"loading",children:"Loading post..."})}):N||!r?e.jsx("div",{className:"single-poem-grid",children:e.jsx("div",{className:"error",children:N||"Post not found"})}):e.jsxs("div",{className:"single-poem-grid",children:[e.jsxs("div",{className:"poem-content",children:[e.jsx("div",{className:"poem-full-title",children:r.title}),e.jsxs("div",{className:"poem-meta",style:{marginBottom:"30px"},children:[e.jsx("div",{className:"author",children:"By Admin"}),e.jsx("div",{className:"date",style:{marginLeft:"20px"},children:$(r.published_at)})]}),e.jsx("div",{className:"poem-text",dangerouslySetInnerHTML:{__html:q(r.content)}})]}),e.jsxs("div",{className:"poem-sidebar",children:[c.length>0&&e.jsxs("div",{className:"sidebar-section",children:[e.jsx("div",{className:"sidebar-title",children:"Categories"}),e.jsx("div",{className:"sidebar-content",children:c.map((t,s)=>e.jsxs("span",{children:[e.jsx(v,{to:`/category/${t.slug}`,style:{color:"#666",textDecoration:"none"},children:t.name}),s<c.length-1&&", "]},t.id))})]}),i.length>0&&e.jsxs("div",{className:"sidebar-section",children:[e.jsx("div",{className:"sidebar-title",children:"Tags"}),e.jsx("div",{className:"sidebar-content",children:i.map((t,s)=>e.jsxs("span",{children:[e.jsx(v,{to:`/tag/${t.slug}`,style:{color:"#666",textDecoration:"none"},children:t.name}),s<i.length-1&&", "]},t.id))})]}),p.length>0&&e.jsxs("div",{className:"sidebar-section",children:[e.jsx("div",{className:"sidebar-title",children:"Related Posts"}),e.jsx("div",{className:"sidebar-content",children:p.map(t=>e.jsx("div",{style:{marginBottom:"10px"},children:e.jsx(v,{to:`/${t.slug}`,style:{color:"#666",textDecoration:"none",fontSize:"13px",display:"block"},children:t.title})},t.id))})]})]})]})};export{C as default};
