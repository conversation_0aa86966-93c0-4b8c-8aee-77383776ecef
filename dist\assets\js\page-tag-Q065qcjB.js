var w=(i,l,o)=>new Promise((g,s)=>{var f=e=>{try{a(o.next(e))}catch(d){s(d)}},x=e=>{try{a(o.throw(e))}catch(d){s(d)}},a=e=>e.done?g(e.value):Promise.resolve(e.value).then(f,x);a((o=o.apply(i,l)).next())});import{a as b,r as p,j as t}from"./react-vendor-DNThP37t.js";import{s as N}from"./utils-G5iJCnJd.js";import{P as S}from"./components-DKpPwVyy.js";import"./vendor-BttnBCBn.js";import"./supabase-vendor-DDc5weSN.js";const T=({searchQuery:i})=>{const{slug:l}=b(),[o,g]=p.useState([]),[s,f]=p.useState(null),[x,a]=p.useState(!0),[e,d]=p.useState(null);p.useEffect(()=>{l&&v()},[l,i]);const v=()=>w(null,null,function*(){try{a(!0);const{data:n,error:h}=yield N.from("tags").select("id, name, slug, description").eq("slug",l).single();if(h)throw h;f(n);const{data:u,error:j}=yield N.from("post_tags").select(`
          posts (
            id,
            title,
            slug,
            content,
            excerpt,
            author_id,
            published_at
          )
        `).eq("tag_id",n.id);if(j)throw j;let m=(u==null?void 0:u.map(r=>r.posts).filter(r=>r))||[];if(i&&i.trim()){const r=i.toLowerCase();m=m.filter(c=>c.title.toLowerCase().includes(r)||c.content&&c.content.toLowerCase().includes(r))}m.sort((r,c)=>new Date(c.published_at)-new Date(r.published_at)),g(m)}catch(n){d("Failed to load tag posts")}finally{a(!1)}});return x?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:"Loading posts..."})}):e?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"error",children:e})}):o.length===0?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:i?`No posts found with tag "${s==null?void 0:s.name}" for "${i}"`:`No posts found with tag "${s==null?void 0:s.name}"`})}):t.jsxs(t.Fragment,{children:[s&&t.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[t.jsxs("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:["#",s.name]}),s.description&&t.jsx("p",{style:{color:"#666",fontSize:"16px"},children:s.description}),t.jsxs("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[o.length," ",o.length===1?"post":"posts"]})]}),t.jsx("div",{className:"main-grid",children:o.map(n=>t.jsx(S,{post:n},n.id))})]})};export{T as default};
