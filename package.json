{"name": "sayari-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "next build && next export", "lint": "next lint", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true next build", "build:static": "next build", "perf:test": "node scripts/performance-test.js", "perf:build": "npm run build:static && npm run perf:test"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "dotenv": "^17.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1"}}