var m=(n,e,t)=>new Promise((s,r)=>{var i=o=>{try{a(t.next(o))}catch(l){r(l)}},c=o=>{try{a(t.throw(o))}catch(l){r(l)}},a=o=>o.done?s(o.value):Promise.resolve(o.value).then(i,c);a((t=t.apply(n,e)).next())});import{c as u}from"./supabase-vendor-DDc5weSN.js";const E=(n,e=150)=>{if(!n)return"";const t=n.replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").trim();if(t.length<=e)return t;const s=t.substring(0,e),r=s.lastIndexOf(" ");return r>e*.8?s.substring(0,r)+"...":s+"..."},I=n=>{if(!n)return null;const e=n.match(/<img[^>]+src="([^"]*)"[^>]*>/i);return e?e[1]:null},y=n=>{if(!n)return"";const e=new Date(n),s=Math.abs(new Date-e),r=Math.ceil(s/(1e3*60*60*24));return r===1?"Yesterday":r<7?`${r} days ago`:r<30?`${Math.ceil(r/7)} weeks ago`:e.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})},S=(n,e)=>{let t;return function(...r){const i=()=>{clearTimeout(t),n(...r)};clearTimeout(t),t=setTimeout(i,e)}},d=!!(window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function M(n){if("serviceWorker"in navigator){if(new URL("/",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",()=>{const t="/sw.js";d?(h(t,n),navigator.serviceWorker.ready.then(()=>{})):f(t,n)})}}function f(n,e){navigator.serviceWorker.register(n).then(t=>{t.onupdatefound=()=>{const s=t.installing;s!=null&&(s.onstatechange=()=>{s.state==="installed"&&(navigator.serviceWorker.controller?e&&e.onUpdate&&e.onUpdate(t):e&&e.onSuccess&&e.onSuccess(t))})}}).catch(t=>{})}function h(n,e){fetch(n,{headers:{"Service-Worker":"script"}}).then(t=>{const s=t.headers.get("content-type");t.status===404||s!=null&&s.indexOf("javascript")===-1?navigator.serviceWorker.ready.then(r=>{r.unregister().then(()=>{window.location.reload()})}):f(n,e)}).catch(()=>{})}class p{constructor(){this.metrics={},this.observers=[],this.init()}init(){this.trackLCP(),this.trackFID(),this.trackCLS(),this.trackFCP(),this.trackTTFB(),this.trackNavigationTiming(),this.trackResourceTiming()}trackLCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{const s=t.getEntries(),r=s[s.length-1];this.metrics.lcp={value:r.startTime,element:r.element,timestamp:Date.now()},this.reportMetric("LCP",r.startTime)});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}trackFID(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(r=>{this.metrics.fid={value:r.processingStart-r.startTime,timestamp:Date.now()},this.reportMetric("FID",r.processingStart-r.startTime)})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}trackCLS(){if("PerformanceObserver"in window){let e=0;const t=new PerformanceObserver(s=>{s.getEntries().forEach(i=>{i.hadRecentInput||(e+=i.value)}),this.metrics.cls={value:e,timestamp:Date.now()},this.reportMetric("CLS",e)});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}trackFCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(r=>{r.name==="first-contentful-paint"&&(this.metrics.fcp={value:r.startTime,timestamp:Date.now()},this.reportMetric("FCP",r.startTime))})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}}trackTTFB(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const t=e[0],s=t.responseStart-t.requestStart;this.metrics.ttfb={value:s,timestamp:Date.now()},this.reportMetric("TTFB",s)}}}trackNavigationTiming(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const t=e[0];this.metrics.navigation={domContentLoaded:t.domContentLoadedEventEnd-t.domContentLoadedEventStart,loadComplete:t.loadEventEnd-t.loadEventStart,domInteractive:t.domInteractive-t.navigationStart,timestamp:Date.now()}}}}trackResourceTiming(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(r=>{r.initiatorType==="img"&&this.trackImageLoad(r)})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}trackImageLoad(e){const t=e.responseEnd-e.startTime;this.metrics.images||(this.metrics.images=[]),this.metrics.images.push({url:e.name,loadTime:t,size:e.transferSize,timestamp:Date.now()}),t>1e3}reportMetric(e,t){window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t)})}getMetrics(){return this.metrics}getPerformanceScore(){const{lcp:e,fid:t,cls:s,fcp:r}=this.metrics;let i=100;return e&&(e.value>4e3?i-=30:e.value>2500&&(i-=15)),t&&(t.value>300?i-=25:t.value>100&&(i-=10)),s&&(s.value>.25?i-=25:s.value>.1&&(i-=10)),r&&(r.value>3e3?i-=20:r.value>1800&&(i-=10)),Math.max(0,i)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}const C=()=>{if(typeof window!="undefined"){const n=new p;return window.addEventListener("load",()=>{setTimeout(()=>{const e=n.getPerformanceScore()},5e3)}),n}return null},g="https://ckjpejxjpcfmlyopqabt.supabase.co",v="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNranBlanhqcGNmbWx5b3BxYWJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MTk5OTUsImV4cCI6MjA2OTI5NTk5NX0.sdinJPYznrITCIJBijRV2iwA0TSLLsTmLWtTYY37OLE",w=u(g,v,{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"sayari-blog@1.0.0"}}}),b="id, title, slug, excerpt, published_at, status",P=(n=0,e=10,t="")=>m(null,null,function*(){const s=n*e,r=s+e-1;let i=w.from("posts").select(b).eq("status","published").order("published_at",{ascending:!1}).range(s,r);t&&t.trim()&&(i=i.or(`title.ilike.%${t}%,excerpt.ilike.%${t}%`));const{data:c,error:a,count:o}=yield i;if(a)throw new Error(`Failed to fetch posts: ${a.message}`);return{posts:c||[],hasMore:c&&c.length===e,total:o}});export{E as a,P as b,S as d,y as f,I as g,C as i,M as r,w as s};
